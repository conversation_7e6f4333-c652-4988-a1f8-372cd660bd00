{"containerDefinitions": [{"name": "nifi", "image": "apache/nifi", "cpu": 0, "portMappings": [{"name": "nifi-8443-tcp", "containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NIFI_WEB_PROXY_HOST", "value": "nifi.production.crm.waveo.com,nifi-datax.production.crm.local:8443"}], "mountPoints": [{"sourceVolume": "logs", "containerPath": "/opt/nifi/nifi-current/logs", "readOnly": false}, {"sourceVolume": "conf", "containerPath": "/opt/nifi/nifi-current/conf", "readOnly": false}, {"sourceVolume": "database_repository", "containerPath": "/opt/nifi/nifi-current/database_repository", "readOnly": false}, {"sourceVolume": "flowfile_repository", "containerPath": "/opt/nifi/nifi-current/flowfile_repository", "readOnly": false}, {"sourceVolume": "content_repository", "containerPath": "/opt/nifi/nifi-current/content_repository", "readOnly": false}, {"sourceVolume": "provenance_repository", "containerPath": "/opt/nifi/nifi-current/provenance_repository", "readOnly": false}, {"sourceVolume": "state", "containerPath": "/opt/nifi/nifi-current/state", "readOnly": false}], "volumesFrom": [], "secrets": [{"name": "SINGLE_USER_CREDENTIALS_USERNAME", "valueFrom": "/copilot/crm/production/secrets/NIFI_SINGLE_USER_CREDENTIALS_USERNAME"}, {"name": "SINGLE_USER_CREDENTIALS_PASSWORD", "valueFrom": "/copilot/crm/production/secrets/NIFI_SINGLE_USER_CREDENTIALS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/crm-production-nifi", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "datax", "image": "732425754724.dkr.ecr.ap-southeast-1.amazonaws.com/crm/datax:prod", "cpu": 0, "portMappings": [{"name": "target", "containerPort": 3016, "hostPort": 3016, "protocol": "tcp"}], "essential": true, "environment": [{"name": "COPILOT_LB_DNS", "value": "crm-p-Publi-1R3PP9YJJT95V-2102762115.ap-southeast-1.elb.amazonaws.com"}, {"name": "COPILOT_APPLICATION_NAME", "value": "crm"}, {"name": "REPO_REDIS_HOST", "value": "redis-14795.c1.ap-southeast-1-1.ec2.cloud.redislabs.com"}, {"name": "COPILOT_ENVIRONMENT_NAME", "value": "production"}, {"name": "METRICS_HOST", "value": "mission-control.production.crm.local"}, {"name": "METRICS_PORT", "value": "8125"}, {"name": "NIFI_DATAX_HOST", "value": "nifi-datax.production.crm.local"}, {"name": "COPILOT_SERVICE_DISCOVERY_ENDPOINT", "value": "production.crm.local"}, {"name": "REPO_REDIS_PORT", "value": "14795"}, {"name": "ENV_REGION", "value": "ap-southeast-1"}, {"name": "PROVIDER_REDIS_PORT", "value": "18366"}, {"name": "PROVIDER_REDIS_HOST", "value": "redis-18366.c1.ap-southeast-1-1.ec2.cloud.redislabs.com"}, {"name": "NODE_ENV", "value": "production"}, {"name": "COPILOT_SERVICE_NAME", "value": "datax"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "REPO_REDIS_USERNAME", "valueFrom": "/copilot/crm/production/secrets/REPO_REDIS_USERNAME"}, {"name": "PERKD_SECRET_KEY", "valueFrom": "/copilot/crm/production/secrets/PERKD_SECRET_KEY"}, {"name": "REPO_REDIS_PASSWORD", "valueFrom": "/copilot/crm/production/secrets/REPO_REDIS_PASSWORD"}, {"name": "PROVIDER_REDIS_USERNAME", "valueFrom": "/copilot/crm/production/secrets/PROVIDER_REDIS_USERNAME"}, {"name": "PROVIDER_REDIS_PASSWORD", "valueFrom": "/copilot/crm/production/secrets/PROVIDER_REDIS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/crm-production-datax", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}}], "family": "crm-production-nifi-datax", "taskRoleArn": "arn:aws:iam::732425754724:role/crm-production-datax-TaskRole-DBNWNZ34V2WM", "executionRoleArn": "arn:aws:iam::732425754724:role/crm-production-datax-ExecutionRole-10JJLLJWUYAXQ", "networkMode": "awsvpc", "volumes": [{"name": "conf", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-030351117c470ee13", "iam": "ENABLED"}}}, {"name": "logs", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0adac9f6acbd3d1f5", "iam": "ENABLED"}}}, {"name": "database_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0d8a973eaf8682a09", "iam": "ENABLED"}}}, {"name": "flowfile_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0126597ee60cbe692", "iam": "ENABLED"}}}, {"name": "content_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0546ab2ef7e4e9caf", "iam": "ENABLED"}}}, {"name": "provenance_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-033ca2a6053b7cf6b", "iam": "ENABLED"}}}, {"name": "state", "efsVolumeConfiguration": {"fileSystemId": "fs-5dcf481d", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-024133eb7760562ab", "iam": "ENABLED"}}}], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "copilot-application", "value": "crm"}, {"key": "copilot-environment", "value": "production"}, {"key": "copilot-service", "value": "nifi-datax"}]}