{"cluster": "crm-production-Cluster-44TU1Oab8W6z", "serviceName": "crm-production-nifi-datax", "taskDefinition": "crm-production-nifi-datax:1", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-p-Target-nifi/2cfd4c868494de15", "containerName": "nifi", "containerPort": 8443}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-pr-Targe-NWKG75K9GMYA/6954d54a08e4f682", "containerName": "datax", "containerPort": 3016}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:ap-southeast-1:732425754724:service/srv-mfcwgspzpl546c2g"}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-08ef9992482446959", "subnet-03da993ccd5bcb3fc"], "securityGroups": ["sg-0198a7b8e1f6ac66e", "sg-0a14ec363de3e32ac"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 90, "schedulingStrategy": "REPLICA", "enableECSManagedTags": false, "propagateTags": "SERVICE", "enableExecuteCommand": true}