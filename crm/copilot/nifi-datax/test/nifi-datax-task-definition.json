{"taskDefinitionArn": "arn:aws:ecs:ap-southeast-1:732425754724:task-definition/crm-test-nifi-datax:12", "containerDefinitions": [{"name": "nifi", "image": "apache/nifi", "cpu": 512, "portMappings": [{"name": "nifi-8443-tcp", "containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NIFI_WEB_PROXY_HOST", "value": "nifi.test.crm.waveo.com,nifi-datax.test.crm.local:8443"}], "mountPoints": [{"sourceVolume": "logs", "containerPath": "/opt/nifi/nifi-current/logs", "readOnly": false}, {"sourceVolume": "conf", "containerPath": "/opt/nifi/nifi-current/conf", "readOnly": false}, {"sourceVolume": "database_repository", "containerPath": "/opt/nifi/nifi-current/database_repository", "readOnly": false}, {"sourceVolume": "flowfile_repository", "containerPath": "/opt/nifi/nifi-current/flowfile_repository", "readOnly": false}, {"sourceVolume": "content_repository", "containerPath": "/opt/nifi/nifi-current/content_repository", "readOnly": false}, {"sourceVolume": "provenance_repository", "containerPath": "/opt/nifi/nifi-current/provenance_repository", "readOnly": false}, {"sourceVolume": "state", "containerPath": "/opt/nifi/nifi-current/state", "readOnly": false}], "volumesFrom": [], "secrets": [{"name": "SINGLE_USER_CREDENTIALS_USERNAME", "valueFrom": "/copilot/crm/test/secrets/NIFI_SINGLE_USER_CREDENTIALS_USERNAME"}, {"name": "SINGLE_USER_CREDENTIALS_PASSWORD", "valueFrom": "/copilot/crm/test/secrets/NIFI_SINGLE_USER_CREDENTIALS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/crm-test-nifi", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "datax", "image": "732425754724.dkr.ecr.ap-southeast-1.amazonaws.com/crm/datax:test", "cpu": 256, "portMappings": [{"name": "target", "containerPort": 3016, "hostPort": 3016, "protocol": "tcp"}], "essential": true, "environment": [{"name": "COPILOT_LB_DNS", "value": "crm-t-Publi-2P6SW8571PB2-1633272148.ap-southeast-1.elb.amazonaws.com"}, {"name": "COPILOT_APPLICATION_NAME", "value": "crm"}, {"name": "REPO_REDIS_HOST", "value": "redis-17294.c292.ap-southeast-1-1.ec2.cloud.redislabs.com"}, {"name": "COPILOT_ENVIRONMENT_NAME", "value": "test"}, {"name": "METRICS_HOST", "value": "mission-control.test.crm.local"}, {"name": "METRICS_PORT", "value": "8125"}, {"name": "NIFI_DATAX_HOST", "value": "nifi-datax.test.crm.local"}, {"name": "COPILOT_SERVICE_DISCOVERY_ENDPOINT", "value": "test.crm.local"}, {"name": "REPO_REDIS_PORT", "value": "17294"}, {"name": "ENV_REGION", "value": "ap-southeast-1"}, {"name": "PROVIDER_REDIS_PORT", "value": "18368"}, {"name": "PROVIDER_REDIS_HOST", "value": "redis-18368.c1.ap-southeast-1-1.ec2.cloud.redislabs.com"}, {"name": "NODE_ENV", "value": "test"}, {"name": "COPILOT_SERVICE_NAME", "value": "datax"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "REPO_REDIS_USERNAME", "valueFrom": "/copilot/crm/test/secrets/REPO_REDIS_USERNAME"}, {"name": "PERKD_SECRET_KEY", "valueFrom": "/copilot/crm/test/secrets/PERKD_SECRET_KEY"}, {"name": "REPO_REDIS_PASSWORD", "valueFrom": "/copilot/crm/test/secrets/REPO_REDIS_PASSWORD"}, {"name": "PROVIDER_REDIS_USERNAME", "valueFrom": "/copilot/crm/test/secrets/PROVIDER_REDIS_USERNAME"}, {"name": "PROVIDER_REDIS_PASSWORD", "valueFrom": "/copilot/crm/test/secrets/PROVIDER_REDIS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/crm-test-datax", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}}], "family": "crm-test-nifi-datax", "taskRoleArn": "arn:aws:iam::732425754724:role/crm-test-datax-TaskRole-zcjhRPloPxVK", "executionRoleArn": "arn:aws:iam::732425754724:role/crm-test-datax-ExecutionRole-MofGloWppb4c", "networkMode": "awsvpc", "revision": 12, "volumes": [{"name": "conf", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-041fbb7b234fbce1d", "iam": "ENABLED"}}}, {"name": "logs", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-06a0a2fa098327cb7", "iam": "ENABLED"}}}, {"name": "database_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-00f90905c6cef20c7", "iam": "ENABLED"}}}, {"name": "flowfile_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0a76b0d95bf91d158", "iam": "ENABLED"}}}, {"name": "content_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-03bc725598d5c9f65", "iam": "ENABLED"}}}, {"name": "provenance_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-019244654bda0d7d4", "iam": "ENABLED"}}}, {"name": "state", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-07f535eb73d3e0490", "iam": "ENABLED"}}}], "status": "ACTIVE", "requiresAttributes": [{"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "ecs.capability.secrets.ssm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}, {"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.efsAuth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.efs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.25"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "copilot-application", "value": "crm"}, {"key": "copilot-environment", "value": "test"}, {"key": "copilot-service", "value": "nifi-datax"}]}