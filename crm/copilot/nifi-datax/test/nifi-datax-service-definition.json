{"cluster": "crm-test-Cluster-xq8M3haJbYYP", "serviceName": "crm-test-nifi-datax", "taskDefinition": "crm-test-nifi-datax:12", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-t-Target-nifi/62399e025ced7555", "containerName": "nifi", "containerPort": 8443}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-te-Targe-Z5W52MYEMINQ/4956e5e3b7d8a9a8", "containerName": "datax", "containerPort": 3016}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:ap-southeast-1:732425754724:service/srv-j5aiphlwfba57ion"}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-07e05b039f9f6c599", "subnet-001cdb37baf812b95"], "securityGroups": ["sg-030dbc14fe4406e28", "sg-0c341613e24881205"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 90, "schedulingStrategy": "REPLICA", "enableECSManagedTags": false, "propagateTags": "SERVICE", "enableExecuteCommand": true}