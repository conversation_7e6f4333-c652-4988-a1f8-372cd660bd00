# The manifest for the "mongo-event" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: mongo-event
type: Backend Service

# Your service is reachable at "http://mongo-event.${COPILOT_SERVICE_DISCOVERY_ENDPOINT}:27017" but is not public.

# Configuration for your containers and service.
image:
  location: mongo:5.0.3
  # Port exposed through your container to route traffic to it.
  port: 27017
  healthcheck: 
    command: ["CMD", "mongo", "--eval", "db.runCommand('ping')"]
    interval: 5s           # amount of time, in seconds, between health checks
    timeout: 2s            # amount of time, in seconds, during which no response from a target means a failed health check
    start_period: 0s       # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
# Auto-scaling:
# count:
#   range: 1-10
#   cpu_percentage: 70
#   memory_percentage: 80
#   requests: 10000
#   response_time: 2s

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

# Optional fields for more advanced use-cases.
#
#variables:                    # Pass environment variables as key value pairs.
#  LOG_LEVEL: info

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

storage:
  volumes:
    db:
      path: '/data/db'                  # Required. The path inside the container.
      read_only: false                          # Default: true
      efs:
        # id: <filesystem ID>                     # Required.
        # root_dir: <filesystem root>             # Optional. Defaults to "/". Must not be specified if using access points.
        # auth:
          # iam: <boolean>                      # Optional. Whether to use IAM authorization when mounting this filesystem.
          # access_point_id: <access point ID>  # Optional. The ID of the EFS Access Point to use when mounting this filesystem.
        uid: 999                                # Optional. UID for managed EFS access point.
        gid: 999

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1               # Number of tasks to run for the "test" environment.
  production:
    count: 1