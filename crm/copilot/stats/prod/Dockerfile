FROM prom/prometheus:v2.52.0 as prometheus
FROM prom/statsd-exporter:v0.26.0 as statsd
FROM grafana/grafana:10.4.0 as grafana
USER root
ENV GF_SMTP_ENABLED=true
ENV GF_SMTP_HOST=smtp.sendgrid.net:25
ENV GF_SMTP_USER=apikey
ENV GF_SMTP_PASSWORD=*********************************************************************
ENV GF_SMTP_SKIP_VERIFY=true
ENV GF_SMTP_FROM_ADDRESS=<EMAIL>
ENV GF_SMTP_FROM_NAME=Perkd
COPY --from=prometheus /bin/prometheus /bin/prometheus
COPY --from=prometheus /prometheus /prometheus
COPY --from=prometheus /usr/share/prometheus /usr/share/prometheus
COPY --from=prometheus /etc/prometheus /etc/prometheus
COPY --from=prometheus /bin/promtool /bin/promtool
COPY --from=prometheus /bin/prometheus /bin/prometheus
COPY --from=statsd /bin/statsd_exporter /bin/statsd_exporter
COPY prometheus.yml /etc/prometheus/prometheus.yml
COPY mapping.yml /tmp/mapping.yml
COPY web.yml /tmp/web.yml
ENTRYPOINT ["/bin/sh", "-c", "/run.sh & /bin/statsd_exporter --statsd.mapping-config=/tmp/mapping.yml --statsd.listen-udp=:8125 & /bin/prometheus --config.file=/etc/prometheus/prometheus.yml --storage.tsdb.path=/prometheus --web.console.libraries=/usr/share/prometheus/console_libraries --web.console.templates=/usr/share/prometheus/consoles --storage.tsdb.retention.time=100d --web.enable-admin-api  --web.config.file=/tmp/web.yml"]