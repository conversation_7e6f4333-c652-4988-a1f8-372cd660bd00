FROM prom/prometheus:v2.50.1 as prometheus
FROM prom/statsd-exporter:v0.26.0 as statsd
FROM grafana/grafana:10.3.4 as grafana
USER root
COPY --from=prometheus /bin/prometheus /bin/prometheus
COPY --from=prometheus /prometheus /prometheus
COPY --from=prometheus /usr/share/prometheus /usr/share/prometheus
COPY --from=prometheus /etc/prometheus /etc/prometheus
COPY --from=prometheus /bin/promtool /bin/promtool
COPY --from=prometheus /bin/prometheus /bin/prometheus
COPY --from=statsd /bin/statsd_exporter /bin/statsd_exporter
COPY prometheus.yml /etc/prometheus/prometheus.yml
COPY mapping.yml /tmp/mapping.yml
COPY web.yml /tmp/web.yml
ENTRYPOINT ["/bin/sh", "-c", "/run.sh & /bin/statsd_exporter --statsd.mapping-config=/tmp/mapping.yml --statsd.listen-udp=:8125 & /bin/prometheus --config.file=/etc/prometheus/prometheus.yml --storage.tsdb.path=/prometheus --web.console.libraries=/usr/share/prometheus/console_libraries --web.console.templates=/usr/share/prometheus/consoles --web.enable-admin-api --web.config.file=/tmp/web.yml"]