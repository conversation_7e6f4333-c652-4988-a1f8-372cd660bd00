{"family": "crm-test-mission-control", "containerDefinitions": [{"name": "mission-control", "image": "732425754724.dkr.ecr.ap-southeast-1.amazonaws.com/crm/mission-control:test", "cpu": 0, "links": [], "portMappings": [{"name": "mission-control-3000-tcp", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}, {"name": "mission-control-9102-tcp", "containerPort": 9102, "hostPort": 9102, "protocol": "tcp"}, {"containerPort": 8125, "hostPort": 8125, "protocol": "udp"}, {"name": "mission-control-9090-tcp", "containerPort": 9090, "hostPort": 9090, "protocol": "tcp"}], "essential": true, "environment": [{"name": "GF_SERVER_ROOT_URL", "value": "https://grafana.mission-control.test.crm.waveo.com/"}], "mountPoints": [{"sourceVolume": "db", "containerPath": "/prometheus", "readOnly": false}, {"sourceVolume": "db", "containerPath": "/var/lib/grafana", "readOnly": false}], "volumesFrom": [], "dockerSecurityOptions": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/crm-test-mission-control", "awslogs-region": "ap-southeast-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::732425754724:role/crm-test-mission-control-TaskRole", "executionRoleArn": "arn:aws:iam::732425754724:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [{"name": "db", "efsVolumeConfiguration": {"fileSystemId": "fs-5b6bee1b", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-09f0ce312a029bbee", "iam": "ENABLED"}}}], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "ecs:taskDefinition:createdFrom", "value": "ecs-console-v2"}]}