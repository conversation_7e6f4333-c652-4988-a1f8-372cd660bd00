# Dockerfile of Docker images
Temp. doc for reference when building stats/Dockerfile.

## Image prom/prometheus
0   WORKDIR /home/<USER>
1	/bin/sh -c #(nop) CMD ["--config.file=/etc/prometheus/prometheus.yml" "--storage.tsdb.path=/prometheus" "--web.console.libraries=/usr/share/prometheus/console_libraries" "--web.console.templates=/usr/share/prometheus/consoles"]	0 Bytes
2	/bin/sh -c #(nop) ENTRYPOINT ["/bin/prometheus"]	0 Bytes
3	/bin/sh -c #(nop) VOLUME [/prometheus]	0 Bytes
4	/bin/sh -c #(nop) EXPOSE 9090	0 Bytes
5	/bin/sh -c #(nop) USER nobody	0 Bytes
6	|2 ARCH=amd64 OS=linux /bin/sh -c ln -s /usr/share/prometheus/console_libraries /usr/share/prometheus/consoles/ /etc/prometheus/ && chown -R nobody:nobody /etc/prometheus /prometheus	1 KB
7	/bin/sh -c #(nop) WORKDIR /prometheus	0 Bytes
8	/bin/sh -c #(nop) COPY file:1447fd13109776d68c3e6918df16994761c53807a40f92f8d5257e813f822d27 in /npm_licenses.tar.bz2	32 KB
9	/bin/sh -c #(nop) COPY file:e56be853b56584e378b90e4f1cb6e3e955d2ddd0e108792ec47a2253d3dfd6a1 in /NOTICE	3.65 KB
10	/bin/sh -c #(nop) COPY file:141c5dcfe0148c05d4c525671bb74da0e5c9e9a18e065974069cb945005f0bb3 in /LICENSE	11.36 KB
11	/bin/sh -c #(nop) COPY dir:36e99d9be0f993d7fcca007c6d99e70ad6a7b0c54ffc1607769cfc1acf53d840 in /usr/share/prometheus/consoles/	19.49 KB
12	/bin/sh -c #(nop) COPY dir:6111a57e3d623c34c589208861b0de74585a44e342acdea27fdc4dbcb0415a3a in /usr/share/prometheus/console_libraries/	9.04 KB
13	/bin/sh -c #(nop) COPY file:a1aaf2bddcc0da1d7ebaf7b5737c5e0341b463504dd919162cb8407f132c3cb8 in /etc/prometheus/prometheus.yml	934 Bytes
14	/bin/sh -c #(nop) COPY file:a4163f24d65fd7c6484084c47eca01a7197d9b6031f930392b78356b96bbf034 in /bin/promtool	90.41 MB
15	/bin/sh -c #(nop) COPY file:3b852748a41a5f5c4982b00226814de3a3e652595110855e40aa30d065167821 in /bin/prometheus	100.99 MB
16	/bin/sh -c #(nop) ARG OS=linux	0 Bytes
17	/bin/sh -c #(nop) ARG ARCH=amd64	0 Bytes
18	/bin/sh -c #(nop) LABEL maintainer=The Prometheus Authors <<EMAIL>>	0 Bytes
19	/bin/sh -c #(nop) COPY dir:bb5589ed25434b0b558cbb90afcfcf5d8a00daa153b27dbdf5a969f9984d9b5a in /	1.44 MB
20	/bin/sh -c #(nop) MAINTAINER The Prometheus Authors <<EMAIL>>	0 Bytes
21	/bin/sh -c #(nop) CMD ["sh"]	0 Bytes
22	/bin/sh -c #(nop) ADD file:dc794c2febce9ec5b68ca4f55027eb4e7d42fc7941e15ba578585d8a166d2d13 in /	1.24 MB

## Image prom/statsd-exporter
0	WORKDIR /home/<USER>
1	/bin/sh -c #(nop) ENTRYPOINT ["/bin/statsd_exporter"]	0 Bytes
2	/bin/sh -c #(nop) HEALTHCHECK &{["CMD-SHELL" "wget --spider -S \"http://localhost:9102/metrics\" -T 60 2>&1 || exit 1"] "0s" "0s" "0s" '\x00'}	0 Bytes
3	/bin/sh -c #(nop) EXPOSE 9102 9125 9125/udp	0 Bytes
4	/bin/sh -c #(nop) USER nobody	0 Bytes
5	/bin/sh -c #(nop) COPY file:005ea2962ed9e1ed8d1e08513922f85ccee0225d1e101b8f203557d5561e53c3 in /bin/statsd_exporter	13.88 MB
6	/bin/sh -c #(nop) ARG OS=linux	0 Bytes
7	/bin/sh -c #(nop) ARG ARCH=amd64	0 Bytes
8	/bin/sh -c #(nop) LABEL maintainer=The Prometheus Authors <<EMAIL>>	0 Bytes
9	/bin/sh -c #(nop) COPY dir:bb5589ed25434b0b558cbb90afcfcf5d8a00daa153b27dbdf5a969f9984d9b5a in /	1.44 MB
10	/bin/sh -c #(nop) MAINTAINER The Prometheus Authors <<EMAIL>>	0 Bytes
11	/bin/sh -c #(nop) CMD ["sh"]	0 Bytes
12	/bin/sh -c #(nop) ADD file:dc794c2febce9ec5b68ca4f55027eb4e7d42fc7941e15ba578585d8a166d2d13 in /	1.24 MB

## Image grafana/grafana
0	WORKDIR /home/<USER>
1	ENTRYPOINT ["/run.sh"]	0 Bytes
2	USER 472	0 Bytes
3	COPY ./run.sh /run.sh # buildkit	3.31 KB
4	EXPOSE map[3000/tcp:{}]	0 Bytes
5	RUN |2 GF_UID=472 GF_GID=0 /bin/sh -c export GF_GID_NAME=$(getent group $GF_GID | cut -d':' -f1) && mkdir -p "$GF_PATHS_HOME/.aws" && adduser -S -u $GF_UID -G "$GF_GID_NAME" grafana && mkdir -p "$GF_PATHS_PROVISIONING/datasources" "$GF_PATHS_PROVISIONING/dashboards" "$GF_PATHS_PROVISIONING/notifiers" "$GF_PATHS_PROVISIONING/plugins" "$GF_PATHS_PROVISIONING/access-control" "$GF_PATHS_LOGS" "$GF_PATHS_PLUGINS" "$GF_PATHS_DATA" && cp "$GF_PATHS_HOME/conf/sample.ini" "$GF_PATHS_CONFIG" && cp "$GF_PATHS_HOME/conf/ldap.toml" /etc/grafana/ldap.toml && chown -R "grafana:$GF_GID_NAME" "$GF_PATHS_DATA" "$GF_PATHS_HOME/.aws" "$GF_PATHS_LOGS" "$GF_PATHS_PLUGINS" "$GF_PATHS_PROVISIONING" && chmod -R 777 "$GF_PATHS_DATA" "$GF_PATHS_HOME/.aws" "$GF_PATHS_LOGS" "$GF_PATHS_PLUGINS" "$GF_PATHS_PROVISIONING" # buildkit	49.78 KB
6	RUN |2 GF_UID=472 GF_GID=0 /bin/sh -c if [ ! $(getent group "$GF_GID") ]; then addgroup -S -g $GF_GID grafana; fi # buildkit	0 Bytes
7	COPY /tmp/grafana /usr/share/grafana # buildkit	224.49 MB
8	RUN |2 GF_UID=472 GF_GID=0 /bin/sh -c if [ `arch` = "x86_64" ]; then apk add --no-cache libaio libnsl && ln -s /usr/lib/libnsl.so.2 /usr/lib/libnsl.so.1 && wget https://github.com/sgerrand/alpine-pkg-glibc/releases/download/2.30-r0/glibc-2.30-r0.apk -O /tmp/glibc-2.30-r0.apk && wget https://github.com/sgerrand/alpine-pkg-glibc/releases/download/2.30-r0/glibc-bin-2.30-r0.apk -O /tmp/glibc-bin-2.30-r0.apk && apk add --no-cache --allow-untrusted /tmp/glibc-2.30-r0.apk /tmp/glibc-bin-2.30-r0.apk && rm -f /tmp/glibc-2.30-r0.apk && rm -f /tmp/glibc-bin-2.30-r0.apk && rm -f /lib/ld-linux-x86-64.so.2 && rm -f /etc/ld.so.cache; fi # buildkit	14.1 MB
9	RUN |2 GF_UID=472 GF_GID=0 /bin/sh -c apk add --no-cache ca-certificates bash tzdata && apk add --no-cache openssl musl-utils # buildkit	4.6 MB
10	WORKDIR /usr/share/grafana	0 Bytes
11	ENV PATH=/usr/share/grafana/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin GF_PATHS_CONFIG=/etc/grafana/grafana.ini GF_PATHS_DATA=/var/lib/grafana GF_PATHS_HOME=/usr/share/grafana GF_PATHS_LOGS=/var/log/grafana GF_PATHS_PLUGINS=/var/lib/grafana/plugins GF_PATHS_PROVISIONING=/etc/grafana/provisioning	0 Bytes
12	ARG GF_GID=0	0 Bytes
13	ARG GF_UID=472	0 Bytes
14	/bin/sh -c #(nop) CMD ["/bin/sh"]	0 Bytes
15	/bin/sh -c #(nop) ADD file:aad4290d27580cc1a094ffaf98c3ca2fc5d699fe695dfb8e6e9fac20f1129450 in /	5.6 MB