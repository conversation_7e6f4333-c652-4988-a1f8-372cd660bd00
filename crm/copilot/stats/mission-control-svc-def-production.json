{"cluster": "crm-production-Cluster-44TU1Oab8W6z", "serviceName": "crm-production-mission-control-Service", "taskDefinition": "crm-production-mission-control:2", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-p-Targe-MC-grafana/6ab17094d835aa68", "containerName": "mission-control", "containerPort": 3000}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-p-Targe-MC-prometheus/bbc640e61bcbb35d", "containerName": "mission-control", "containerPort": 9090}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-p-Targe-MC-statsd/5d2b0b4c397f4ead", "containerName": "mission-control", "containerPort": 8125}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:ap-southeast-1:732425754724:service/srv-aoytfiodsyhg3adm"}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-03da993ccd5bcb3fc", "subnet-08ef9992482446959"], "securityGroups": ["sg-0a14ec363de3e32ac", "sg-0a86ccea6960e75d5"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 5, "schedulingStrategy": "REPLICA", "enableECSManagedTags": false, "propagateTags": "SERVICE", "enableExecuteCommand": true}