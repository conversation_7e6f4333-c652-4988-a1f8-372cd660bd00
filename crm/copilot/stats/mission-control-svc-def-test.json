{"cluster": "crm-test-Cluster-xq8M3haJbYYP", "serviceName": "crm-test-mission-control-Service", "taskDefinition": "crm-test-mission-control:11", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-t-Targe-MC-grafana/1433c899dd73b2bc", "containerName": "mission-control", "containerPort": 3000}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-t-Targe-MC-prometheus/27566d267fc223f2", "containerName": "mission-control", "containerPort": 9090}, {"targetGroupArn": "arn:aws:elasticloadbalancing:ap-southeast-1:732425754724:targetgroup/crm-test-anne-tg-nlb-statsd/625df52431da3117", "containerName": "mission-control", "containerPort": 8125}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:ap-southeast-1:732425754724:service/srv-3i44rxj2yffis3br"}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-07e05b039f9f6c599", "subnet-001cdb37baf812b95"], "securityGroups": ["sg-030dbc14fe4406e28", "sg-08e887cc9250b9a9f"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 5, "schedulingStrategy": "REPLICA", "enableECSManagedTags": false, "propagateTags": "SERVICE", "enableExecuteCommand": true}