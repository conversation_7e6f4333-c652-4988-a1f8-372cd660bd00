# Stats Docker image (WIP)

TODO:
1. Ensure all dockerfile commands of 3 images are effective and not in conflict for multi-stage builds
2. Test the 3 images are functioning properly
3. Feel free to make the dockerfile better (i.e. best practices, reduce unnecessary files)

# Build image
docker build -t stats .
# Run container
docker run -d -p 3000:3000 -p 9102:9102 -p 9090:9090 -p 8125:8125/udp --name stats stats