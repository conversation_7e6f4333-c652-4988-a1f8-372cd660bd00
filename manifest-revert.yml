# The manifest for the "redis" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: redis
type: Backend Service

# Configuration for your containers and service.
image:
  location: redis:7.2.1-alpine
  # Port exposed through your container to route traffic to it.
  port: 6379
  healthcheck:
    command: ["CMD", "sh", "-c", "redis-cli ping || (ENCODED_PASS=$(echo \"$EVENTBUS_DEFAULT_PASSWORD\" | sed 's/%/%25/g; s/&/%26/g; s/@/%40/g' 2>/dev/null); redis-cli -u \"redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASS@localhost:6379\" ping 2>/dev/null)"]
    retries: 3
    interval: 20s           # amount of time, in seconds, between health checks
    timeout: 30s            # amount of time, in seconds, during which no response from a target means a failed health check
    start_period: 60s       # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries

taskdef_overrides:
# Allow redis maxclients 100000
  - path: ContainerDefinitions[0].Ulimits[-]
    value:
      Name: "nofile"
      SoftLimit: 100035
      HardLimit: 100035

# Override entrypoint to forcefully clean authentication from redis.conf
  - path: ContainerDefinitions[0].EntryPoint
    value:
      - 'sh'
      - '-c'

# Safe revert from authenticated Redis to no-auth Redis with data preservation
  - path: ContainerDefinitions[0].Command
    value:
      - |
        set -e
        echo "=== REDIS AUTHENTICATION REVERT - Safe Data Preservation ==="

        # Set default memory limit if not provided
        REDIS_MAXMEMORY=${REDIS_MAXMEMORY:-400mb}
        echo "Using Redis memory limit: $REDIS_MAXMEMORY"

        # URL encode function for passwords with special characters
        url_encode() {
          echo "$1" | sed 's/%/%25/g; s/ /%20/g; s/!/%21/g; s/"/%22/g; s/#/%23/g; s/\$/%24/g; s/&/%26/g; s/'\''/%27/g; s/(/%28/g; s/)/%29/g; s/\*/%2A/g; s/+/%2B/g; s/,/%2C/g; s/-/%2D/g; s/\./%2E/g; s/\//%2F/g; s/:/%3A/g; s/;/%3B/g; s/</%3C/g; s/=/%3D/g; s/>/%3E/g; s/?/%3F/g; s/@/%40/g; s/\[/%5B/g; s/\\/%5C/g; s/\]/%5D/g; s/\^/%5E/g; s/_/%5F/g; s/`/%60/g; s/{/%7B/g; s/|/%7C/g; s/}/%7D/g; s/~/%7E/g'
        }

        # Create backup directory with timestamp
        BACKUP_DIR="/data/backup-revert-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        echo "Created backup directory: $BACKUP_DIR"

        # Check current Redis state and handle authentication
        REDIS_RUNNING=false
        EXISTING_DATA=false
        AUTH_REQUIRED=false

        if pgrep redis-server > /dev/null; then
          echo "Redis server is already running, attempting graceful revert..."
          REDIS_RUNNING=true

          # Test if we can connect without authentication first
          if redis-cli ping > /dev/null 2>&1; then
            echo "✅ Connected to existing Redis without authentication (already reverted?)"
          else
            echo "Redis requires authentication - attempting authenticated connection..."
            AUTH_REQUIRED=true

            # Try to connect with authentication if credentials are available
            if [ -n "$EVENTBUS_DEFAULT_USERNAME" ] && [ -n "$EVENTBUS_DEFAULT_PASSWORD" ]; then
              ENCODED_PASSWORD=$(url_encode "$EVENTBUS_DEFAULT_PASSWORD")
              if redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASSWORD@localhost:6379" ping > /dev/null 2>&1; then
                echo "✅ Connected to authenticated Redis - proceeding with revert"

                # Check if there's existing data
                KEYCOUNT=$(redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASSWORD@localhost:6379" dbsize 2>/dev/null || echo "0")
                if [ "$KEYCOUNT" -gt 0 ]; then
                  echo "📊 Found $KEYCOUNT keys in authenticated Redis instance"
                  EXISTING_DATA=true

                  # Create data backup
                  echo "Creating data backup from authenticated Redis..."
                  redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASSWORD@localhost:6379" --rdb "$BACKUP_DIR/dump.rdb" || echo "Warning: RDB backup failed"

                  # If AOF is enabled, backup AOF file
                  if [ -f /data/appendonly.aof ]; then
                    cp /data/appendonly.aof "$BACKUP_DIR/appendonly.aof.backup"
                    echo "✅ AOF file backed up"
                  fi
                fi

                # Gracefully shutdown authenticated Redis
                echo "Gracefully shutting down authenticated Redis..."
                redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASSWORD@localhost:6379" shutdown nosave || pkill redis-server
                sleep 3
              else
                echo "❌ Cannot connect to Redis with provided credentials"
                echo "Forcing shutdown for reconfiguration..."
                pkill redis-server || true
                sleep 2
              fi
            else
              echo "⚠️  No authentication credentials provided - forcing shutdown"
              pkill redis-server || true
              sleep 2
            fi
          fi
        fi

        # Backup existing configuration
        if [ -f /data/redis.conf ]; then
          cp /data/redis.conf "$BACKUP_DIR/redis.conf.backup"
          echo "✅ Existing redis.conf backed up"
        fi

        # Create new redis.conf WITHOUT authentication
        echo "Creating redis.conf WITHOUT authentication..."
        cat > /data/redis.conf << EOF
        # Redis configuration - NO AUTHENTICATION
        bind 0.0.0.0
        port 6379
        protected-mode no

        # === NO AUTHENTICATION CONFIG ===
        # Default user enabled with no password (Redis default behavior)
        # No user commands - Redis will accept connections without password

        # === DATA PERSISTENCE ===
        dir /data
        appendonly yes
        appendfilename "appendonly.aof"
        appendfsync everysec
        auto-aof-rewrite-percentage 100
        auto-aof-rewrite-min-size 64mb

        # RDB snapshots (backup)
        save 900 1
        save 300 10
        save 60 10000

        # Memory management
        maxmemory-policy allkeys-lru
        maxmemory $REDIS_MAXMEMORY

        # Logging
        loglevel notice
        logfile ""

        # Performance tuning
        tcp-keepalive 300
        timeout 0
        maxclients 20000

        # Security (minimal for no-auth setup)
        rename-command FLUSHDB ""
        rename-command FLUSHALL ""
        EOF

        echo "✅ Redis configuration WITHOUT authentication created"

        # Start Redis without authentication
        echo "Starting Redis without authentication..."
        redis-server /data/redis.conf &
        REDIS_PID=$!

        # Wait for Redis to start
        echo "Waiting for Redis to start..."
        sleep 10

        # Test no-auth connection
        echo "Testing no-authentication connection..."
        NO_AUTH_TEST_RESULT=0
        redis-cli ping || NO_AUTH_TEST_RESULT=$?

        if [ $NO_AUTH_TEST_RESULT -eq 0 ]; then
          echo "✅ No-authentication connection working successfully"

          # If we had existing data, verify it's accessible
          if [ "$EXISTING_DATA" = true ]; then
            FINAL_KEYCOUNT=$(redis-cli dbsize 2>/dev/null || echo "0")
            echo "📊 Data verification - Current key count: $FINAL_KEYCOUNT"

            # If data is missing, try to restore from backup
            if [ "$FINAL_KEYCOUNT" -eq 0 ] && [ -f "$BACKUP_DIR/dump.rdb" ]; then
              echo "⚠️  Data appears to be missing - attempting restore from backup..."
              redis-cli shutdown nosave || true
              sleep 2

              # Copy backup to data directory
              cp "$BACKUP_DIR/dump.rdb" /data/dump.rdb 2>/dev/null || echo "Warning: Could not copy RDB backup"

              # Restart Redis
              redis-server /data/redis.conf &
              REDIS_PID=$!
              sleep 5

              RESTORED_KEYCOUNT=$(redis-cli dbsize 2>/dev/null || echo "0")
              echo "📊 After restore attempt - Key count: $RESTORED_KEYCOUNT"
            fi
          fi

          # Final status
          FINAL_KEYCOUNT=$(redis-cli dbsize 2>/dev/null || echo "0")
          echo "✅ Redis revert completed successfully"
          echo "📊 Final key count: $FINAL_KEYCOUNT"
          echo "🔓 Authentication DISABLED - Redis accepts connections without password"
          echo "💾 Data persistence enabled (AOF + RDB)"
          echo "📁 Backup location: $BACKUP_DIR"

          # Keep Redis running in foreground
          wait $REDIS_PID
        else
          echo "❌ No-authentication test failed - rolling back"
          kill $REDIS_PID 2>/dev/null || true

          # Attempt rollback if backup exists
          if [ -f "$BACKUP_DIR/redis.conf.backup" ]; then
            echo "Attempting rollback to previous configuration..."
            cp "$BACKUP_DIR/redis.conf.backup" /data/redis.conf
            redis-server /data/redis.conf &
            sleep 5

            # Test rollback - try both auth and no-auth
            if redis-cli ping > /dev/null 2>&1; then
              echo "✅ Rollback successful - Redis running with previous config (no-auth)"
              wait $!
            elif [ "$AUTH_REQUIRED" = true ] && [ -n "$EVENTBUS_DEFAULT_USERNAME" ] && [ -n "$EVENTBUS_DEFAULT_PASSWORD" ]; then
              ENCODED_PASSWORD=$(url_encode "$EVENTBUS_DEFAULT_PASSWORD")
              if redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASSWORD@localhost:6379" ping > /dev/null 2>&1; then
                echo "✅ Rollback successful - Redis running with previous config (authenticated)"
                wait $!
              else
                echo "❌ Rollback failed"
                exit 1
              fi
            else
              echo "❌ Rollback failed"
              exit 1
            fi
          else
            echo "❌ No backup available for rollback"
            exit 1
          fi
        fi

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/x86_64

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

storage:
  volumes:
    db:
      path: '/data'                  # Required. The path inside the container.
      read_only: false                          # Default: true
      efs:
        uid: 999                                # Optional. UID for managed EFS access point.
        gid: 999

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1               # Number of tasks to run for the "test" environment.
    variables:
      REDIS_MAXMEMORY: "400mb"  # Default memory limit for test environment
  production:
    cpu: 2048
    memory: 8192
    count: 1
    variables:
      REDIS_MAXMEMORY: "7gb"  # Set to ~85% of allocated memory for safety