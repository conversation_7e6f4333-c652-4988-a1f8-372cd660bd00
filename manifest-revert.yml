# The manifest for the "redis" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: redis
type: Backend Service

# Configuration for your containers and service.
image:
  location: redis:7.2.1-alpine
  # Port exposed through your container to route traffic to it.
  port: 6379
  healthcheck:
    command: ["CMD", "redis-cli", "ping"]
    interval: 5s           # amount of time, in seconds, between health checks
    timeout: 2s            # amount of time, in seconds, during which no response from a target means a failed health check
    start_period: 0s       # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries

taskdef_overrides:
# Allow redis maxclients 100000
  - path: ContainerDefinitions[0].Ulimits[-]
    value:
      Name: "nofile"
      SoftLimit: 100035
      HardLimit: 100035

# Override entrypoint to forcefully clean authentication from redis.conf
  - path: ContainerDefinitions[0].EntryPoint
    value:
      - 'sh'
      - '-c'

# Completely clean redis.conf and start fresh
  - path: ContainerDefinitions[0].Command
    value:
      - |
        set -e
        echo "Force cleaning Redis configuration - removing ALL authentication..."
        
        # Check if redis.conf exists and show current content
        if [ -f redis.conf ]; then
          echo "Current redis.conf content:"
          cat redis.conf
          echo "=========================="
        fi
        
        # Create a completely clean redis.conf without any authentication
        cat > redis.conf << 'EOF'
        # Redis configuration - NO AUTHENTICATION
        bind 0.0.0.0
        port 6379
        protected-mode no
        
        # Data persistence
        dir /data
        appendonly yes
        appendfilename "appendonly.aof"
        appendfsync everysec
        
        # Memory management
        maxmemory-policy allkeys-lru
        
        # Logging
        loglevel notice
        logfile ""
        
        # Performance tuning
        tcp-keepalive 300
        timeout 0
        
        # Client limits
        maxclients 20000
        
        # NO USER AUTHENTICATION - Redis will accept connections without password
        EOF
        
        echo "Clean redis.conf created successfully"
        echo "New redis.conf content:"
        cat redis.conf
        echo "=========================="
        echo "Starting Redis server without any authentication..."
        exec redis-server redis.conf

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/x86_64

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

storage:
  volumes:
    db:
      path: '/data'                  # Required. The path inside the container.
      read_only: false                          # Default: true
      efs:
        uid: 999                                # Optional. UID for managed EFS access point.
        gid: 999

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1               # Number of tasks to run for the "test" environment.
  production:
    cpu: 1024
    memory: 4096
    count: 1