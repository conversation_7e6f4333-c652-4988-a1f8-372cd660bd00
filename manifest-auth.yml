# The manifest for the "redis" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: redis
type: Backend Service

# Your service does not allow any traffic.

# Configuration for your containers and service.
image:
  location: redis:7.2.1-alpine
  # Port exposed through your container to route traffic to it.
  port: 6379
  healthcheck:
    command: ["CMD", "sh", "-c", "ENCODED_PASS=$(echo \"$EVENTBUS_DEFAULT_PASSWORD\" | sed 's/%/%25/g; s/&/%26/g; s/@/%40/g'); redis-cli -u \"redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_PASS@localhost:6379\" ping || redis-cli ping"]
    retries: 3
    interval: 20s           # amount of time, in seconds, between health checks
    timeout: 30s            # amount of time, in seconds, during which no response from a target means a failed health check
    start_period: 60s       # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries

# Retrieve passwords from AWS Systems Manager Parameter Store
secrets:
  EVENTBUS_DEFAULT_USERNAME: /copilot/crm/test/secrets/EVENTBUS_DEFAULT_USERNAME
  EVENTBUS_DEFAULT_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_DEFAULT_PASSWORD
  EVENTBUS_READ_USERNAME: /copilot/crm/test/secrets/EVENTBUS_READ_USERNAME
  EVENTBUS_READ_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_READ_PASSWORD
  EVENTBUS_WRITE_USERNAME: /copilot/crm/test/secrets/EVENTBUS_WRITE_USERNAME
  EVENTBUS_WRITE_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_WRITE_PASSWORD
  EVENTBUS_READWRITE_USERNAME: /copilot/crm/test/secrets/EVENTBUS_READWRITE_USERNAME
  EVENTBUS_READWRITE_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_READWRITE_PASSWORD

taskdef_overrides:
# IMPORTANT
# Set redis.conf maxclients {value} + 35
  - path: ContainerDefinitions[0].Ulimits[-]
    value:
      Name: "nofile"
      SoftLimit: 20035
      HardLimit: 20035

# Override entrypoint to add authentication to existing redis.conf then start Redis
  - path: ContainerDefinitions[0].EntryPoint
    value:
      - 'sh'
      - '-c'

  - path: ContainerDefinitions[0].Command
    value:
      - |
        set -e
        echo "=== REDIS AUTHENTICATION MIGRATION - Safe Data Preservation ==="

        # Validate required environment variables
        if [ -z "$EVENTBUS_DEFAULT_USERNAME" ] || [ -z "$EVENTBUS_DEFAULT_PASSWORD" ] || \
           [ -z "$EVENTBUS_READWRITE_USERNAME" ] || [ -z "$EVENTBUS_READWRITE_PASSWORD" ] || \
           [ -z "$EVENTBUS_READ_USERNAME" ] || [ -z "$EVENTBUS_READ_PASSWORD" ] || \
           [ -z "$EVENTBUS_WRITE_USERNAME" ] || [ -z "$EVENTBUS_WRITE_PASSWORD" ]; then
          echo "❌ ERROR: Required authentication environment variables are missing"
          echo "Required: EVENTBUS_DEFAULT_USERNAME, EVENTBUS_DEFAULT_PASSWORD, EVENTBUS_READWRITE_USERNAME, EVENTBUS_READWRITE_PASSWORD, EVENTBUS_READ_USERNAME, EVENTBUS_READ_PASSWORD, EVENTBUS_WRITE_USERNAME, EVENTBUS_WRITE_PASSWORD"
          exit 1
        fi

        # Set default memory limit if not provided
        REDIS_MAXMEMORY=${REDIS_MAXMEMORY:-400mb}
        echo "Using Redis memory limit: $REDIS_MAXMEMORY"

        # URL encode function for passwords with special characters
        url_encode() {
          local input="$1"
          # Handle the most common problematic characters
          input=$(echo "$input" | sed 's/%/%25/g')
          input=$(echo "$input" | sed 's/&/%26/g')
          input=$(echo "$input" | sed 's/@/%40/g')
          input=$(echo "$input" | sed 's/ /%20/g')
          input=$(echo "$input" | sed 's/:/%3A/g')
          input=$(echo "$input" | sed 's/?/%3F/g')
          input=$(echo "$input" | sed 's/#/%23/g')
          echo "$input"
        }

        # URL encode passwords for URI connections
        ENCODED_DEFAULT_PASSWORD=$(url_encode "$EVENTBUS_DEFAULT_PASSWORD")
        ENCODED_READWRITE_PASSWORD=$(url_encode "$EVENTBUS_READWRITE_PASSWORD")
        ENCODED_READ_PASSWORD=$(url_encode "$EVENTBUS_READ_PASSWORD")
        ENCODED_WRITE_PASSWORD=$(url_encode "$EVENTBUS_WRITE_PASSWORD")

        # Create backup directory with timestamp
        BACKUP_DIR="/data/backup-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        echo "Created backup directory: $BACKUP_DIR"

        # Check if Redis is currently running and try to connect without auth
        REDIS_RUNNING=false
        EXISTING_DATA=false

        if pgrep redis-server > /dev/null; then
          echo "Redis server is already running, attempting graceful migration..."
          REDIS_RUNNING=true

          # Test if we can connect without authentication (current state)
          if redis-cli ping > /dev/null 2>&1; then
            echo "✅ Connected to existing Redis without authentication"

            # Check if there's existing data
            KEYCOUNT=$(redis-cli dbsize 2>/dev/null || echo "0")
            if [ "$KEYCOUNT" -gt 0 ]; then
              echo "📊 Found $KEYCOUNT keys in existing Redis instance"
              EXISTING_DATA=true

              # Create data backup
              echo "Creating data backup..."
              redis-cli --rdb "$BACKUP_DIR/dump.rdb" || echo "Warning: RDB backup failed"

              # If AOF is enabled, backup AOF file
              if [ -f /data/appendonly.aof ]; then
                cp /data/appendonly.aof "$BACKUP_DIR/appendonly.aof.backup"
                echo "✅ AOF file backed up"
              fi
            fi

            # Gracefully shutdown existing Redis
            echo "Gracefully shutting down existing Redis..."
            redis-cli shutdown nosave || pkill redis-server
            sleep 3
          else
            echo "Existing Redis requires authentication - stopping for reconfiguration..."
            pkill redis-server || true
            sleep 2
          fi
        fi

        # Backup existing configuration
        if [ -f /data/redis.conf ]; then
          cp /data/redis.conf "$BACKUP_DIR/redis.conf.backup"
          echo "✅ Existing redis.conf backed up"
        fi

        # Create new redis.conf with authentication
        echo "Creating redis.conf with authentication..."
        cat > /data/redis.conf << EOF
        # Redis configuration with authentication
        bind 0.0.0.0
        port 6379
        protected-mode no

        # === AUTHENTICATION CONFIG ===
        # Disable default user for security
        user default off

        # Admin user (full access)
        user $EVENTBUS_DEFAULT_USERNAME on >$EVENTBUS_DEFAULT_PASSWORD ~* &* +@all

        # Application user (read-write for streams and general operations)
        user $EVENTBUS_READWRITE_USERNAME on >$EVENTBUS_READWRITE_PASSWORD ~* &* +@read +@write +@stream +@connection +@keyspace +@string +@hash +@list +@set +@sortedset +@bitmap +@hyperloglog +@geo +@pubsub +info +ping +client

        # Read-only user for monitoring/debugging
        user $EVENTBUS_READ_USERNAME on >$EVENTBUS_READ_PASSWORD ~* &* +@read +@stream +@connection +info +ping +client|list +client|id +client|info

        # Write-only user for data ingestion
        user $EVENTBUS_WRITE_USERNAME on >$EVENTBUS_WRITE_PASSWORD ~* &* +@write +@stream +@connection +info +ping +publish

        # === DATA PERSISTENCE ===
        dir /data
        appendonly yes
        appendfilename "appendonly.aof"
        appendfsync everysec
        auto-aof-rewrite-percentage 100
        auto-aof-rewrite-min-size 64mb

        # RDB snapshots (backup)
        save 900 1
        save 300 10
        save 60 10000

        # Memory management
        maxmemory-policy allkeys-lru
        maxmemory $REDIS_MAXMEMORY

        # Logging
        loglevel notice
        logfile ""

        # Performance tuning
        tcp-keepalive 300
        timeout 0
        maxclients 20000

        # Security
        rename-command FLUSHDB ""
        rename-command FLUSHALL ""
        rename-command DEBUG ""
        rename-command CONFIG ADMIN_CONFIG
        EOF

        echo "✅ Redis configuration with authentication created"

        # Start Redis with new authentication configuration
        echo "Starting Redis with authentication..."
        redis-server /data/redis.conf &
        REDIS_PID=$!

        # Wait for Redis to start
        echo "Waiting for Redis to start..."
        sleep 10

        # Test authentication
        echo "Testing authentication..."
        AUTH_TEST_RESULT=0
        redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_DEFAULT_PASSWORD@localhost:6379" ping || AUTH_TEST_RESULT=$?

        if [ $AUTH_TEST_RESULT -eq 0 ]; then
          echo "✅ Authentication working successfully"

          # If we had existing data, try to restore it
          if [ "$EXISTING_DATA" = true ] && [ -f "$BACKUP_DIR/dump.rdb" ]; then
            echo "Attempting to restore existing data..."
            redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_DEFAULT_PASSWORD@localhost:6379" DEBUG RESTART || echo "Warning: Could not restart for data restoration"
          fi

          # Verify all user accounts work
          echo "Verifying user accounts..."
          redis-cli -u "redis://$EVENTBUS_READWRITE_USERNAME:$ENCODED_READWRITE_PASSWORD@localhost:6379" ping && echo "✅ ReadWrite user OK" || echo "❌ ReadWrite user failed"
          redis-cli -u "redis://$EVENTBUS_READ_USERNAME:$ENCODED_READ_PASSWORD@localhost:6379" ping && echo "✅ Read user OK" || echo "❌ Read user failed"
          redis-cli -u "redis://$EVENTBUS_WRITE_USERNAME:$ENCODED_WRITE_PASSWORD@localhost:6379" ping && echo "✅ Write user OK" || echo "❌ Write user failed"

          # Final status
          FINAL_KEYCOUNT=$(redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$ENCODED_DEFAULT_PASSWORD@localhost:6379" dbsize 2>/dev/null || echo "0")
          echo "✅ Redis migration completed successfully"
          echo "📊 Current key count: $FINAL_KEYCOUNT"
          echo "🔐 Authentication enabled with 4 user accounts"
          echo "💾 Data persistence enabled (AOF + RDB)"
          echo "📁 Backup location: $BACKUP_DIR"

          # Keep Redis running in foreground
          wait $REDIS_PID
        else
          echo "❌ Authentication test failed - rolling back"
          kill $REDIS_PID 2>/dev/null || true

          # Attempt rollback if backup exists
          if [ -f "$BACKUP_DIR/redis.conf.backup" ]; then
            echo "Attempting rollback to previous configuration..."
            cp "$BACKUP_DIR/redis.conf.backup" /data/redis.conf
            redis-server /data/redis.conf &
            sleep 5
            if redis-cli ping > /dev/null 2>&1; then
              echo "✅ Rollback successful - Redis running with previous config"
              wait $!
            else
              echo "❌ Rollback failed"
              exit 1
            fi
          else
            echo "❌ No backup available for rollback"
            exit 1
          fi
        fi

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/x86_64
# Auto-scaling:
# count:
#   range: 1-10
#   cpu_percentage: 70
#   memory_percentage: 80
#   requests: 10000
#   response_time: 2s

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

storage:
  volumes:
    db:
      path: '/data'                  # Required. The path inside the container.
      read_only: false                          # Default: true
      efs:
        # id: <filesystem ID>                     # Required.
        # root_dir: <filesystem root>             # Optional. Defaults to "/". Must not be specified if using access points.
        # auth:
          # iam: <boolean>                      # Optional. Whether to use IAM authorization when mounting this filesystem.
          # access_point_id: <access point ID>  # Optional. The ID of the EFS Access Point to use when mounting this filesystem.
        uid: 999                                # Optional. UID for managed EFS access point.
        gid: 999

# Optional fields for more advanced use-cases.
#
#variables:                    # Pass environment variables as key value pairs.
#  LOG_LEVEL: info

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1               # Number of tasks to run for the "test" environment.
    variables:
      REDIS_MAXMEMORY: "400mb"  # Default memory limit for test environment
  production:
    cpu: 2048
    memory: 8192
    count: 1
    variables:
      REDIS_MAXMEMORY: "7gb"  # Set to ~85% of allocated memory for safety
    secrets:
      EVENTBUS_DEFAULT_USERNAME: /copilot/crm/production/secrets/EVENTBUS_DEFAULT_USERNAME
      EVENTBUS_DEFAULT_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_DEFAULT_PASSWORD
      EVENTBUS_READ_USERNAME: /copilot/crm/production/secrets/EVENTBUS_READ_USERNAME
      EVENTBUS_READ_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_READ_PASSWORD
      EVENTBUS_WRITE_USERNAME: /copilot/crm/production/secrets/EVENTBUS_WRITE_USERNAME
      EVENTBUS_WRITE_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_WRITE_PASSWORD
      EVENTBUS_READWRITE_USERNAME: /copilot/crm/production/secrets/EVENTBUS_READWRITE_USERNAME
      EVENTBUS_READWRITE_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_READWRITE_PASSWORD