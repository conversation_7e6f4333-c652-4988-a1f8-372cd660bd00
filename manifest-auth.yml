# The manifest for the "redis" service.
# Read the full specification for the "Backend Service" type at:
#  https://aws.github.io/copilot-cli/docs/manifest/backend-service/

# Your service name will be used in naming your resources like log groups, ECS services, etc.
name: redis
type: Backend Service

# Your service does not allow any traffic.

# Configuration for your containers and service.
image:
  location: redis:7.2.1-alpine
  # Port exposed through your container to route traffic to it.
  port: 6379
  healthcheck:
    command: ["CMD", "redis-cli", "-u", "redis://admin:jL3K&cM5%nD7sGh@JkLpQzXcVbYnMtRf@localhost:6379", "ping"]
    retries: 3
    interval: 20s           # amount of time, in seconds, between health checks
    timeout: 30s            # amount of time, in seconds, during which no response from a target means a failed health check
    start_period: 0s       # grace period within which to provide containers time to bootstrap before failed health checks count towards the maximum number of retries

# Retrieve passwords from AWS Systems Manager Parameter Store
secrets:
  EVENTBUS_DEFAULT_USERNAME: /copilot/crm/test/secrets/EVENTBUS_DEFAULT_USERNAME
  EVENTBUS_DEFAULT_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_DEFAULT_PASSWORD
  EVENTBUS_READ_USERNAME: /copilot/crm/test/secrets/EVENTBUS_READ_USERNAME
  EVENTBUS_READ_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_READ_PASSWORD
  EVENTBUS_WRITE_USERNAME: /copilot/crm/test/secrets/EVENTBUS_WRITE_USERNAME
  EVENTBUS_WRITE_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_WRITE_PASSWORD
  EVENTBUS_READWRITE_USERNAME: /copilot/crm/test/secrets/EVENTBUS_READWRITE_USERNAME
  EVENTBUS_READWRITE_PASSWORD: /copilot/crm/test/secrets/EVENTBUS_READWRITE_PASSWORD

taskdef_overrides:
# IMPORTANT
# Set redis.conf maxclients {value} + 35
  - path: ContainerDefinitions[0].Ulimits[-]
    value:
      Name: "nofile"
      SoftLimit: 20035
      HardLimit: 20035

# Override entrypoint to add authentication to existing redis.conf then start Redis
  - path: ContainerDefinitions[0].EntryPoint
    value:
      - 'sh'
      - '-c'

  - path: ContainerDefinitions[0].Command
    value:
      - |
        set -e
        echo "=== AGGRESSIVE AOF CLEANUP - Redis Auth Setup ==="
        
        # Kill any running Redis processes
        pkill redis-server || true
        sleep 2
        
        # Remove ALL AOF-related files completely
        echo "Removing all AOF-related files..."
        rm -f /data/appendonly.aof*
        rm -f /data/*.rdb
        rm -f /data/dump.rdb
        rm -f /data/temp-*.aof
        rm -f /data/temp-*.rdb
        
        # Create backup directory
        BACKUP_DIR="/data/backup-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # Backup any existing config
        if [ -f /data/redis.conf ]; then
          cp /data/redis.conf "$BACKUP_DIR/redis.conf.backup"
        fi
        
        # Create redis.conf with authentication BUT NO AOF initially
        echo "Creating redis.conf with auth but NO persistence initially..."
        cat > /data/redis.conf << EOF
        # Redis configuration with authentication - NO AOF initially
        bind 0.0.0.0
        port 6379
        protected-mode no
        
        # === AUTHENTICATION CONFIG ===
        # Disable default user for security
        user default off
        
        # Admin user (full access)
        user $EVENTBUS_DEFAULT_USERNAME on >$EVENTBUS_DEFAULT_PASSWORD ~* &* +@all
        
        # Application user (read-write for streams)
        user $EVENTBUS_READWRITE_USERNAME on >$EVENTBUS_READWRITE_PASSWORD ~* &* +@read +@write +xread +xreadgroup +xadd +xgroup +xtrim +xdel +xinfo +xpending +xlen +xrange +xrevrange +client +info +publish +subscribe
        
        # Read-only user for monitoring/debugging
        user $EVENTBUS_READ_USERNAME on >$EVENTBUS_READ_PASSWORD ~* &* +@read +xread +xreadgroup +xgroup +xinfo +xpending +xlen +xrange +xrevrange +client|id
        
        # Write-only user
        user $EVENTBUS_WRITE_USERNAME on >$EVENTBUS_WRITE_PASSWORD ~* &* +@write +xadd +xgroup +xtrim +xdel +info +publish
        
        # === NO PERSISTENCE INITIALLY ===
        dir /data
        # appendonly no   # Disabled initially
        save ""           # Disable RDB snapshots initially
        
        # Memory management
        maxmemory-policy allkeys-lru
        maxmemory 400mb
        
        # Logging
        loglevel notice
        logfile ""
        
        # Performance tuning
        tcp-keepalive 300
        timeout 0
        maxclients 20000
        EOF
        
        echo "Starting Redis with authentication but NO persistence..."
        redis-server /data/redis.conf &
        REDIS_PID=$!
        
        # Wait for Redis to start
        sleep 5
        
        # Test authentication works
        echo "Testing authentication..."
        redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$EVENTBUS_DEFAULT_PASSWORD@localhost:6379" ping
        
        if [ $? -eq 0 ]; then
          echo "✅ Authentication working successfully"
          
          # Now enable AOF through CONFIG command (safer)
          echo "Enabling AOF persistence through CONFIG command..."
          redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$EVENTBUS_DEFAULT_PASSWORD@localhost:6379" CONFIG SET appendonly yes
          redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$EVENTBUS_DEFAULT_PASSWORD@localhost:6379" CONFIG SET appendfilename "appendonly.aof"
          redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$EVENTBUS_DEFAULT_PASSWORD@localhost:6379" CONFIG SET appendfsync everysec
          
          # Save the config with AOF enabled
          redis-cli -u "redis://$EVENTBUS_DEFAULT_USERNAME:$EVENTBUS_DEFAULT_PASSWORD@localhost:6379" CONFIG REWRITE
          
          echo "✅ AOF persistence enabled successfully"
          echo "Redis is running with authentication and fresh AOF"
          
          # Keep Redis running in foreground
          wait $REDIS_PID
        else
          echo "❌ Authentication test failed"
          kill $REDIS_PID 2>/dev/null || true
          exit 1
        fi

cpu: 256       # Number of CPU units for the task.
memory: 512    # Amount of memory in MiB used by the task.
count: 1       # Number of tasks that should be running in your service.
platform: linux/x86_64
# Auto-scaling:
# count:
#   range: 1-10
#   cpu_percentage: 70
#   memory_percentage: 80
#   requests: 10000
#   response_time: 2s

exec: true     # Enable running commands in your container.

network:
  vpc:
    placement: 'private'

storage:
  volumes:
    db:
      path: '/data'                  # Required. The path inside the container.
      read_only: false                          # Default: true
      efs:
        # id: <filesystem ID>                     # Required.
        # root_dir: <filesystem root>             # Optional. Defaults to "/". Must not be specified if using access points.
        # auth:
          # iam: <boolean>                      # Optional. Whether to use IAM authorization when mounting this filesystem.
          # access_point_id: <access point ID>  # Optional. The ID of the EFS Access Point to use when mounting this filesystem.
        uid: 999                                # Optional. UID for managed EFS access point.
        gid: 999

# Optional fields for more advanced use-cases.
#
#variables:                    # Pass environment variables as key value pairs.
#  LOG_LEVEL: info

#secrets:                      # Pass secrets from AWS Systems Manager (SSM) Parameter Store.
#  GITHUB_TOKEN: GITHUB_TOKEN  # The key is the name of the environment variable, the value is the name of the SSM parameter.

# You can override any of the values defined above by environment.
environments:
  test:
    count: 1               # Number of tasks to run for the "test" environment.
  production:
    cpu: 2048
    # IMPORTANT:
    # Set maxmemory in redis.conf to memory {below} first before deploying
    memory: 8192
    count: 1
    secrets:
      EVENTBUS_DEFAULT_USERNAME: /copilot/crm/production/secrets/EVENTBUS_DEFAULT_USERNAME
      EVENTBUS_DEFAULT_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_DEFAULT_PASSWORD
      EVENTBUS_READ_USERNAME: /copilot/crm/production/secrets/EVENTBUS_READ_USERNAME
      EVENTBUS_READ_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_READ_PASSWORD
      EVENTBUS_WRITE_USERNAME: /copilot/crm/production/secrets/EVENTBUS_WRITE_USERNAME
      EVENTBUS_WRITE_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_WRITE_PASSWORD
      EVENTBUS_READWRITE_USERNAME: /copilot/crm/production/secrets/EVENTBUS_READWRITE_USERNAME
      EVENTBUS_READWRITE_PASSWORD: /copilot/crm/production/secrets/EVENTBUS_READWRITE_PASSWORD