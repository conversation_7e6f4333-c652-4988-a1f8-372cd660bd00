{"containerDefinitions": [{"name": "nifi", "image": "apache/nifi", "cpu": 0, "portMappings": [{"name": "nifi-8443-tcp", "containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NIFI_WEB_PROXY_HOST", "value": "nifi.production.perkd.perkd.me,nifi-datax.production.perkd.local:8443"}], "mountPoints": [{"sourceVolume": "logs", "containerPath": "/opt/nifi/nifi-current/logs", "readOnly": false}, {"sourceVolume": "conf", "containerPath": "/opt/nifi/nifi-current/conf", "readOnly": false}, {"sourceVolume": "database_repository", "containerPath": "/opt/nifi/nifi-current/database_repository", "readOnly": false}, {"sourceVolume": "flowfile_repository", "containerPath": "/opt/nifi/nifi-current/flowfile_repository", "readOnly": false}, {"sourceVolume": "content_repository", "containerPath": "/opt/nifi/nifi-current/content_repository", "readOnly": false}, {"sourceVolume": "provenance_repository", "containerPath": "/opt/nifi/nifi-current/provenance_repository", "readOnly": false}, {"sourceVolume": "state", "containerPath": "/opt/nifi/nifi-current/state", "readOnly": false}], "volumesFrom": [], "secrets": [{"name": "SINGLE_USER_CREDENTIALS_USERNAME", "valueFrom": "/copilot/perkd/production/secrets/NIFI_SINGLE_USER_CREDENTIALS_USERNAME"}, {"name": "SINGLE_USER_CREDENTIALS_PASSWORD", "valueFrom": "/copilot/perkd/production/secrets/NIFI_SINGLE_USER_CREDENTIALS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/perkd-production-nifi", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "ecs"}}}, {"name": "datax", "image": "732425754724.dkr.ecr.us-west-2.amazonaws.com/perkd/datax:prod", "cpu": 0, "portMappings": [{"name": "target", "containerPort": 8016, "hostPort": 8016, "protocol": "tcp"}], "essential": true, "environment": [{"name": "COPILOT_LB_DNS", "value": "perkd-Publi-27I0TGJ2DJ1Z-1081872682.us-west-2.elb.amazonaws.com"}, {"name": "COPILOT_APPLICATION_NAME", "value": "perkd"}, {"name": "REPO_REDIS_HOST", "value": "redis-14879.c1.us-west-2-2.ec2.cloud.redislabs.com"}, {"name": "COPILOT_ENVIRONMENT_NAME", "value": "production"}, {"name": "METRICS_HOST", "value": "mission-control.production.crm.local"}, {"name": "METRICS_PORT", "value": "8125"}, {"name": "NIFI_DATAX_HOST", "value": "nifi-datax.production.perkd.local"}, {"name": "COPILOT_SERVICE_DISCOVERY_ENDPOINT", "value": "production.perkd.local"}, {"name": "REPO_REDIS_PORT", "value": "14879"}, {"name": "ENV_REGION", "value": "us-west-2"}, {"name": "PROVIDER_REDIS_PORT", "value": "14879"}, {"name": "PROVIDER_REDIS_HOST", "value": "redis-14879.c1.us-west-2-2.ec2.cloud.redislabs.com"}, {"name": "NODE_ENV", "value": "production"}, {"name": "COPILOT_SERVICE_NAME", "value": "datax"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "REPO_REDIS_USERNAME", "valueFrom": "/copilot/perkd/production/secrets/REPO_REDIS_USERNAME"}, {"name": "PERKD_SECRET_KEY", "valueFrom": "/copilot/perkd/production/secrets/PERKD_SECRET_KEY"}, {"name": "REPO_REDIS_PASSWORD", "valueFrom": "/copilot/perkd/production/secrets/REPO_REDIS_PASSWORD"}, {"name": "PROVIDER_REDIS_USERNAME", "valueFrom": "/copilot/perkd/production/secrets/PROVIDER_REDIS_USERNAME"}, {"name": "PROVIDER_REDIS_PASSWORD", "valueFrom": "/copilot/perkd/production/secrets/PROVIDER_REDIS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/perkd-production-datax", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "ecs"}}}], "family": "perkd-production-nifi-datax", "taskRoleArn": "arn:aws:iam::732425754724:role/perkd-production-datax-TaskRole-1HWAU8PW6R56J", "executionRoleArn": "arn:aws:iam::732425754724:role/perkd-production-datax-ExecutionRole-WXMJJW0O406L", "networkMode": "awsvpc", "volumes": [{"name": "conf", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0b0d38f2e57529372", "iam": "ENABLED"}}}, {"name": "logs", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-06f29073d050b0a8f", "iam": "ENABLED"}}}, {"name": "database_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0761a43c8972318eb", "iam": "ENABLED"}}}, {"name": "flowfile_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0fbeab8be8ea84b03", "iam": "ENABLED"}}}, {"name": "content_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-04753afdcf640be65", "iam": "ENABLED"}}}, {"name": "provenance_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0f7a0bf96969c3992", "iam": "ENABLED"}}}, {"name": "state", "efsVolumeConfiguration": {"fileSystemId": "fs-08db400fe966d5482", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-05bc0c056659976bd", "iam": "ENABLED"}}}], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "tags": [{"key": "copilot-application", "value": "perkd"}, {"key": "copilot-environment", "value": "production"}, {"key": "copilot-service", "value": "nifi-datax"}]}