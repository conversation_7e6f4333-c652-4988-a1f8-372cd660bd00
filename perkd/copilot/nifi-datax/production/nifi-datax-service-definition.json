{
    "cluster": "perkd-production-Cluster-zOauwiSYeUcC",
    "serviceName": "perkd-production-nifi-datax",
<<<<<<< HEAD
    "taskDefinition": "perkd-production-nifi-datax:4",
=======
    "taskDefinition": "perkd-production-nifi-datax:5",
>>>>>>> 1163824bd3789235e98fd0683de2fb52fa9103e1
    "loadBalancers": [
        {
            "targetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:732425754724:targetgroup/perkd-p-Target-nifi/77d79830e6c560c6",
            "containerName": "nifi",
            "containerPort": 8443
        },
        {
            "targetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:732425754724:targetgroup/perkd-Targe-6U7EF6A84KNE/9078318655cae39f",
            "containerName": "datax",
            "containerPort": 8016
        }
    ],
    "serviceRegistries": [
        {
            "registryArn": "arn:aws:servicediscovery:us-west-2:732425754724:service/srv-dpe6xnxjmtoshsna"
        }
    ],
    "desiredCount": 1,
    "launchType": "FARGATE",
    "platformVersion": "LATEST",
    "deploymentConfiguration": {
        "deploymentCircuitBreaker": {
            "enable": true,
            "rollback": true
        },
		"maximumPercent": 200,
		"minimumHealthyPercent": 100
    },
    "networkConfiguration": {
		"awsvpcConfiguration": {
			"subnets": [
				"subnet-07e8d5971db096ad3",
				"subnet-0220132fb5060dc9e"
			],
			"securityGroups": [
				"sg-06998348e27480d0d",
                "sg-0441e76b1ca5186ad"
			],
            "assignPublicIp": "ENABLED"
		}
    },
    "healthCheckGracePeriodSeconds": 90,
    "schedulingStrategy": "REPLICA",
    "enableECSManagedTags": false,
    "propagateTags": "SERVICE",
    "enableExecuteCommand": true
}