{"cluster": "perkd-test-Cluster-vuvQBqvb6OaI", "serviceName": "perkd-test-nifi-datax", "taskDefinition": "perkd-test-nifi-datax:1", "loadBalancers": [{"targetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:732425754724:targetgroup/perkd-t-Target-nifi/38071799ac522585", "containerName": "nifi", "containerPort": 8443}, {"targetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:732425754724:targetgroup/perkd-Targe-13MOSNRF4BPVO/e140b5609ce613ec", "containerName": "datax", "containerPort": 8016}], "serviceRegistries": [{"registryArn": "arn:aws:servicediscovery:us-west-2:732425754724:service/srv-3lcy7hcn6uyqt3mj"}], "desiredCount": 1, "launchType": "FARGATE", "platformVersion": "LATEST", "deploymentConfiguration": {"deploymentCircuitBreaker": {"enable": true, "rollback": true}, "maximumPercent": 200, "minimumHealthyPercent": 100}, "networkConfiguration": {"awsvpcConfiguration": {"subnets": ["subnet-00a6d01a8ee321f03", "subnet-0dfdd00e8b38ce1bc"], "securityGroups": ["sg-0cf82871507442606", "sg-0bdf010d4fa007bef"], "assignPublicIp": "ENABLED"}}, "healthCheckGracePeriodSeconds": 90, "schedulingStrategy": "REPLICA", "enableECSManagedTags": false, "propagateTags": "SERVICE", "enableExecuteCommand": true}