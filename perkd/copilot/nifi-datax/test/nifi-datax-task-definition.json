{"taskDefinitionArn": "arn:aws:ecs:us-west-2:732425754724:task-definition/perkd-test-nifi-datax:5", "containerDefinitions": [{"name": "nifi", "image": "apache/nifi", "cpu": 0, "portMappings": [{"name": "nifi-8443-tcp", "containerPort": 8443, "hostPort": 8443, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NIFI_WEB_PROXY_HOST", "value": "nifi.test.perkd.perkd.me,nifi-datax.test.perkd.local:8443"}], "mountPoints": [{"sourceVolume": "logs", "containerPath": "/opt/nifi/nifi-current/logs", "readOnly": false}, {"sourceVolume": "conf", "containerPath": "/opt/nifi/nifi-current/conf", "readOnly": false}, {"sourceVolume": "database_repository", "containerPath": "/opt/nifi/nifi-current/database_repository", "readOnly": false}, {"sourceVolume": "flowfile_repository", "containerPath": "/opt/nifi/nifi-current/flowfile_repository", "readOnly": false}, {"sourceVolume": "content_repository", "containerPath": "/opt/nifi/nifi-current/content_repository", "readOnly": false}, {"sourceVolume": "provenance_repository", "containerPath": "/opt/nifi/nifi-current/provenance_repository", "readOnly": false}, {"sourceVolume": "state", "containerPath": "/opt/nifi/nifi-current/state", "readOnly": false}], "volumesFrom": [], "secrets": [{"name": "SINGLE_USER_CREDENTIALS_USERNAME", "valueFrom": "/copilot/perkd/test/secrets/NIFI_SINGLE_USER_CREDENTIALS_PASSWORD"}, {"name": "SINGLE_USER_CREDENTIALS_PASSWORD", "valueFrom": "/copilot/perkd/test/secrets/NIFI_SINGLE_USER_CREDENTIALS_USERNAME"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/perkd-test-nifi", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "ecs"}}}, {"name": "datax", "image": "732425754724.dkr.ecr.us-west-2.amazonaws.com/perkd/datax:test", "cpu": 0, "portMappings": [{"name": "target", "containerPort": 8016, "hostPort": 8016, "protocol": "tcp"}], "essential": true, "environment": [{"name": "COPILOT_LB_DNS", "value": "perkd-Publi-27I0TGJ2DJ1Z-1081872682.us-west-2.elb.amazonaws.com"}, {"name": "COPILOT_APPLICATION_NAME", "value": "perkd"}, {"name": "REPO_REDIS_HOST", "value": "redis-18122.c1.us-west-2-2.ec2.cloud.redislabs.com"}, {"name": "COPILOT_ENVIRONMENT_NAME", "value": "test"}, {"name": "METRICS_HOST", "value": "mission-control.test.crm.local"}, {"name": "METRICS_PORT", "value": "8125"}, {"name": "NIFI_DATAX_HOST", "value": "nifi-datax.test.perkd.local"}, {"name": "COPILOT_SERVICE_DISCOVERY_ENDPOINT", "value": "test.perkd.local"}, {"name": "REPO_REDIS_PORT", "value": "18122"}, {"name": "ENV_REGION", "value": "us-west-2"}, {"name": "PROVIDER_REDIS_PORT", "value": "18122"}, {"name": "PROVIDER_REDIS_HOST", "value": "redis-18122.c1.us-west-2-2.ec2.cloud.redislabs.com"}, {"name": "NODE_ENV", "value": "test"}, {"name": "COPILOT_SERVICE_NAME", "value": "datax"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "REPO_REDIS_USERNAME", "valueFrom": "/copilot/perkd/test/secrets/REPO_REDIS_USERNAME"}, {"name": "PERKD_SECRET_KEY", "valueFrom": "/copilot/perkd/test/secrets/PERKD_SECRET_KEY"}, {"name": "REPO_REDIS_PASSWORD", "valueFrom": "/copilot/perkd/test/secrets/REPO_REDIS_PASSWORD"}, {"name": "PROVIDER_REDIS_USERNAME", "valueFrom": "/copilot/perkd/test/secrets/PROVIDER_REDIS_USERNAME"}, {"name": "PROVIDER_REDIS_PASSWORD", "valueFrom": "/copilot/perkd/test/secrets/PROVIDER_REDIS_PASSWORD"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/perkd-test-datax", "awslogs-region": "us-west-2", "awslogs-stream-prefix": "ecs"}}}], "family": "perkd-test-nifi-datax", "taskRoleArn": "arn:aws:iam::732425754724:role/perkd-test-datax-TaskRole-1OUI4CGQ4R3HX", "executionRoleArn": "arn:aws:iam::732425754724:role/perkd-test-datax-ExecutionRole-1KAMMYGP2HKHD", "networkMode": "awsvpc", "revision": 5, "volumes": [{"name": "conf", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-08c9c808e5874f11d", "iam": "ENABLED"}}}, {"name": "logs", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0c6195c0628983f45", "iam": "ENABLED"}}}, {"name": "database_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-06f37a30b6ff12ef8", "iam": "ENABLED"}}}, {"name": "flowfile_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0b22c797022891b20", "iam": "ENABLED"}}}, {"name": "content_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-0cdca280a361741ea", "iam": "ENABLED"}}}, {"name": "provenance_repository", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-08a69bfd50181b085", "iam": "ENABLED"}}}, {"name": "state", "efsVolumeConfiguration": {"fileSystemId": "fs-0226003cd57fde2db", "rootDirectory": "/", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-00d069f91975eba2b", "iam": "ENABLED"}}}], "status": "ACTIVE", "requiresAttributes": [{"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "ecs.capability.secrets.ssm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}, {"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.efsAuth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.efs"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.25"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "ARM64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-02-22T12:37:35.057Z", "registeredBy": "arn:aws:iam::732425754724:user/anne", "tags": [{"key": "copilot-application", "value": "perkd"}, {"key": "copilot-environment", "value": "test"}, {"key": "copilot-service", "value": "nifi-datax"}]}