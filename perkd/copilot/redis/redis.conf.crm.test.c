# Redis configuration - NO AUTHENTICATION
bind 0.0.0.0
port 6379
protected-mode no

# Data persistence
dir /data
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec

# Memory management
maxmemory-policy allkeys-lru

# Logging
loglevel notice
logfile ""

# Performance tuning
tcp-keepalive 300
timeout 0

# Client limits
maxclients 20000

# NO USER AUTHENTICATION - Redis will accept connections without password